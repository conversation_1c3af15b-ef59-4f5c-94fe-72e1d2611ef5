/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.ssl.form.itemLabel.expireTime': 'Geçerlilik Süresi',
  'page.ssl.form.itemLabel.cert': 'Sertifika',
  'page.ssl.form.itemRuleMessage.certValueLength':
    'Sertifika dosyasının uzunluğu en az 128 karakter olmalıdır',
  'page.ssl.form.itemLabel.privateKey': '<PERSON><PERSON>',
  'page.ssl.form.itemRuleMessage.privateKeyLength':
    '<PERSON>zel anahtar dosyasının uzunluğu en az 128 karakter olmalıdır',

  'page.ssl.button.uploadCert': 'Sertifika Yükle',

  'page.ssl.form.itemLabel.way': 'Yöntem',
  'page.ssl.select.placeholder.selectCreateWays': 'Yöntem seçin',
  'page.ssl.selectOption.input': 'Giriş',
  'page.ssl.upload': 'Yükle',

  'page.ssl.notification.updateCertEnableStatusSuccessfully':
    'Sertifika etkinleştirme durumu başarıyla güncellendi',
  'page.ssl.list': 'SSL Listesi',
  'page.ssl.list.expirationTime': 'Sertifika Geçerlilik Süresi',
  'page.ssl.list.ifEnable': 'Aktif ise',
  'page.ssl.list.periodOfValidity': 'Geçirlilik Periyodu',
  'page.ssl.steps.stepTitle.completeCertInfo': 'Sertifika Bilgilerini Tamamla',
  'component.ssl.removeSSLSuccess': 'Seçilen SSL başarıyla silindi',
  'component.ssl.removeSSLItemModalContent': 'Bu öğeyi silmek üzeresiniz!',

  'component.ssl.description':
    'Sertifika, ağ geçidi tarafından SNI ile ilişkilendirilecek ve Rotadaki ana bilgisayar adına bağlı olacak şifreli istekleri işlemek için kullanılır.',
  'component.ssl.fields.cert.required': 'Sertifika gerekli',
  'component.ssl.fields.key.required': 'Özel anahtar gerekli',
};
