/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.consumer.form.itemRuleMessage.username':
    'Maks<PERSON>um uzunluk 100dür yalnızca harfler ve sayılar desteklenir.',
  'page.consumer.form.itemExtraMessage.username': 'Adının tekil olması gerekir',
  'page.consumer.notification.warning.enableAuthenticationPlugin':
    'Lütfen aşağıdaki kimlik doğrulama eklentisinden en az birini etkinleştirin:',
  'page.consumer.username': 'Ad',
  'page.consumer.username.required': 'Lütfen kullanıcının adını girin',
  'page.consumer.updateTime': 'Güncelleme Zamanı',
  'page.consumer.list': 'Kullanıcı Listesi',
  'page.consumer.description':
    'Tüketiciler, örneğin geliştiriciler, son kullanıcılar, API çağrıları vb. gibi Routesın tüketicileridir. Bir tüketici oluştururken, en az bir Kimlik Doğrulama eklentisi bağlamanız gerekir.',
  'page.consumer.create': 'Kullanıcı Oluştur',
  'page.consumer.configure': 'Kullanıcı Yapılandır',
};
