apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Release.Name }}
  labels:
    app: {{ .Release.Name }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    release: {{ .Release.Name }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Release.Name }}
      release: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app: {{ .Release.Name }}
        release: {{ .Release.Name }}
    spec:
      # Thêm imagePullSecrets
      imagePullSecrets:
      - name: fptcloud-registry-secret
      containers:
      - name: {{ .Chart.Name }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        ports:
        - containerPort: {{ .Values.service.port }}
        volumeMounts:
        - name: config
          mountPath: /usr/local/apisix-dashboard/conf/conf.yaml
          subPath: conf.yaml
      volumes:
      - name: config
        configMap:
          name: {{ .Release.Name }}-config 