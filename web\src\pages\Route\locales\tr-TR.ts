/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.route.button.returnList': 'Listeye dön',
  'page.route.button.send': '<PERSON><PERSON><PERSON>',
  'page.route.onlineDebug': 'Çevrimiçi Hata Ayıklama',
  'page.route.pluginTemplateConfig': 'Eklenti Şablon Yapılandırma',

  'page.route.parameterPosition': 'Parametre konumu',
  'page.route.httpRequestHeader': 'HTTP isteği başlığı',
  'page.route.requestParameter': 'İstek parametresi',
  'page.route.postRequestParameter': 'POST isteği parametresi',
  'page.route.builtinParameter': 'Dahili parametre',
  'page.route.parameterName': 'Parametre adı',
  'page.route.operationalCharacter': 'İşletim karakteri',
  'page.route.equal': 'Eşit(==)',
  'page.route.unequal': 'Eşit Değildir(~=)',
  'page.route.greaterThan': 'Büyüktür(>)',
  'page.route.lessThan': 'Küçüktür(<)',
  'page.route.regexMatch': 'Regex eşleşmesi(=~)',
  'page.route.caseInsensitiveRegexMatch': 'Büyük küçük harf duyarsız regex eşleşmesi(=~i)',
  'page.route.in': 'İçinde(in)',
  'page.route.has': 'Var(has)',
  'page.route.reverse': 'Ters(reverse)',
  'page.route.rule': 'Kural',
  'page.route.httpHeaderName': 'HTTP isteği başlığı adı',
  'page.route.service': 'Hizmet',
  'page.route.instructions': 'Talimatlar',
  'page.route.import': 'İçe aktar',
  'page.route.createRoute': 'Yeni yönlendirme oluştur',
  'page.route.editRoute': 'Yönlendirme düzenle',
  'page.route.batchDeletion': 'Toplu yolu sil',
  'page.route.unSelect': 'Seçilme',
  'page.route.item': 'term',
  'page.route.chosen': 'Seçili',

  'page.route.input.placeholder.parameterNameHttpHeader': 'Request header adı örn :HOST',
  'page.route.input.placeholder.parameterNameRequestParameter': 'Parameter adı örn :userId',
  'page.route.input.placeholder.requestUrl': 'Geçerli bir Request URL girin',
  'page.route.input.placeholder.paramKey': 'Param Anahtarı',
  'page.route.input.placeholder.paramValue': 'Param Değeri',
  'page.route.input.placeholder.paramType': 'Param Tipi',

  'page.route.form.itemRulesRequiredMessage.parameterName':
    'Parametre adı gerekli ve geçerli bir değer içermelidir.',
  'page.route.value': 'Parametre Değeri',
  'page.route.panelSection.title.advancedMatchRule': 'Gelişmiş Eşleşme Kuralı',

  'page.route.panelSection.title.nameDescription': 'Ad ve Açıklama',
  'page.route.form.itemRulesPatternMessage.apiNameRule': 'Maksimum 100 karakter olmalıdır.',

  'page.route.panelSection.title.requestConfigBasicDefine': 'Basit Yönlendirme Tanımı',
  'page.route.form.itemLabel.httpMethod': 'HTTP Methodu',
  'page.route.form.itemLabel.scheme': 'Şema',
  'page.route.form.itemLabel.priority': 'Öncelik',
  'page.route.form.itemLabel.redirect': 'Yönlendirme',
  'page.route.select.option.enableHttps': 'HTTPS etkinleştir',
  'page.route.select.option.configCustom': 'Özel yapılandırma',
  'page.route.select.option.forbidden': 'Yasaklılar',
  'page.route.form.itemLabel.redirectCustom': 'Özel yönlendirme',
  'page.route.input.placeholder.redirectCustom': 'Örn: /foo/index.html',
  'page.route.select.option.redirect301': '301(Kalıcı Yönlendirme)',
  'page.route.select.option.redirect302': '302(Geçici Yönlendirme)',
  'page.route.form.itemLabel.username': 'Kullanıcı Adı',
  'page.route.form.itemLabel.password': 'Parola',
  'page.route.form.itemLabel.token': 'Token(Jeton)',
  'page.route.form.itemLabel.apikey': 'Api Anahtarı',

  'page.route.form.itemExtraMessage.domain': 'Domain adınızı girin. Örn: www.example.com',
  'page.route.form.itemRulesPatternMessage.domain':
    'Domain adı gerekli ve geçerli bir değer içermelidir.',
  'page.route.form.itemExtraMessage1.path': 'Yönlendirme yolunu girin. Örn: /foo/index.html',
  'page.route.form.itemRulesPatternMessage.remoteAddrs':
    'Geçerli bir IP adresi girin. örn: *************, ***********/24, ::1, fe80::1, fe80::1/64',
  'page.route.form.itemExtraMessage1.remoteAddrs':
    'Client IP girin, örn: *************, ***********/24, ::1, fe80::1, fe80::1/64',

  'page.route.httpAction': 'Aksiyon',
  'page.route.httpOverrideOrCreate': 'Yönlendirme veya Oluştur',
  'page.route.panelSection.title.httpOverrideRequestHeader': 'Yönlendirme İsteği Başlığı',
  'page.route.status': 'Durum',
  'page.route.groupName': 'Grup Adı',
  'page.route.offline': 'Çevrimdışı',
  'page.route.publish': 'Yayınla',
  'page.route.published': 'Yayınlandı',
  'page.route.unpublished': 'Yayınlanmamış',

  'page.route.select.option.inputManually': 'Manuel giriş',
  'page.route.select.option.methodRewriteNone': 'Yönlendirme yok',
  'page.route.form.itemLabel.domainNameOrIp': 'Domain adı veya IP',
  'page.route.form.itemExtraMessage.domainNameOrIp':
    'Etki Alanı Adını kullanırken, varsayılan olarak yerel: /etc/resolv.conf dosyasını analiz eder.',
  'page.route.portNumber': 'Port Numarası',
  'page.route.weight': 'Ağırlık',
  'page.route.radio.staySame': 'Aynısında kal',
  'page.route.form.itemLabel.newPath': 'Yeni Yol',
  'page.route.form.itemLabel.newHost': 'Yeni Host',
  'page.route.form.itemLabel.regex': 'Regex',
  'page.route.form.itemLabel.template': 'Şablon',
  'page.route.form.itemLabel.URIRewriteType': 'URI Yönlendirme Tipi',
  'page.route.form.itemLabel.hostRewriteType': 'Host Yönlendirme Tipi',
  'page.route.form.itemLabel.methodRewrite': 'Method Yönlendirme',
  'page.route.form.itemLabel.redirectURI': 'Yönlendirme URI',
  'page.route.input.placeholder.newPath': 'örn: /foo/bar/index.html',

  'page.route.steps.stepTitle.defineApiRequest': 'API Request tanımla',
  'page.route.steps.stepTitle.defineApiBackendServe': 'API Backend Server tanımla',

  'page.route.popconfirm.title.offline':
    'Bu yönlendirmeyi çevrimdışı yapmak istediğinize emin misiniz?',
  'page.route.radio.static': 'Statik',
  'page.route.radio.regex': 'Regex',
  'page.route.form.itemLabel.from': 'Kimden',
  'page.route.form.itemHelp.status':
    'Bir rota oluşturulduktan sonra kullanılıp kullanılamayacağı, varsayılan değer false şeklindedir.',

  'page.route.host': 'İstemci',
  'page.route.path': 'Yol',
  'page.route.remoteAddrs': 'Uzak Adresler',
  'page.route.PanelSection.title.defineRequestParams': 'Request Parametreleri Tanımla',
  'page.route.PanelSection.title.responseResult': 'Response Sonuçları',
  'page.route.debug.showResultAfterSendRequest': 'Request gönderdikten sonra sonuçları göster',
  'page.route.TabPane.queryParams': 'Query Parametreleri',
  'page.route.TabPane.bodyParams': 'Body Parametreleri',
  'page.route.TabPane.headerParams': 'Header Parametreleri',
  'page.route.TabPane.authentication': 'Kimlik Doğrulama',
  'page.route.TabPane.response': 'Response',
  'page.route.TabPane.header': 'Response Header',
  'page.route.debugWithoutAuth': 'Bu istek herhangi bir yetkilendirme kullanmaz.',
  'page.route.button.exportOpenApi': 'OpenAPI Dışarı Aktar',
  'page.route.exportRoutesTips': 'Lütfen dışa aktarmak istediğiniz dosyanın türünü seçin',
  'page.route.button.importOpenApi': 'OpenAPI İçeri Aktar',
  'page.route.button.selectFile': 'Dosya Seç',
  'page.route.list': 'Rotalar',
  'page.route.panelSection.title.requestOverride': 'Yönlendirme İsteği',
  'page.route.form.itemLabel.headerRewrite': 'Header Yönlendirme',
  'page.route.tooltip.pluginOrchOnlySupportChrome':
    'Eklenti düzenlemesi yalnızca Chromeu destekler.',
  'page.route.tooltip.pluginOrchWithoutProxyRewrite':
    'Adım 1de istek geçersiz kılma yapılandırıldığında eklenti düzenleme modu kullanılamaz.',
  'page.route.tooltip.pluginOrchWithoutRedirect':
    'HTTPSyi etkinleştirmek için Adım 1de Yeniden Yönlendir seçildiğinde eklenti düzenleme modu kullanılamaz.',

  'page.route.tabs.normalMode': 'Normal',
  'page.route.tabs.orchestration': 'Orkestrasyon',

  'page.route.list.description':
    'Rota, bir istemci isteği ile bir hizmet arasındaki eşleştirme kurallarını tanımlayan bir isteğin giriş noktasıdır. Bir yol bir hizmetle (Hizmet), bir yukarı akışla (Upstream) ilişkilendirilebilir, bir hizmet bir dizi rotaya karşılık gelebilir ve bir rota bir yukarı akış nesnesine (bir dizi arka uç hizmet düğümü) karşılık gelebilir, böylece her istek eşleşir. bir rotaya, ağ geçidi tarafından rotaya bağlı yukarı akış hizmetine vekalet edilecektir.',
  'page.route.configuration.name.rules.required.description': 'Rota adı zorunlu bir alandır.',
  'page.route.configuration.name.placeholder': 'Rota adı girin',
  'page.route.configuration.desc.tooltip': 'Rota açıklaması girin',
  'page.route.configuration.publish.tooltip':
    'Bir rotanın oluşturulduktan hemen sonra ağ geçidine yayınlanıp yayınlanmayacağını kontrol etmek için kullanılır',
  'page.route.configuration.version.placeholder': 'Rota sürümü girin',
  'page.route.configuration.version.tooltip':
    'Rota sürümü şu şekilde olabilir: 1.0.0, 1.0.1, 1.0.2',
  'page.route.configuration.normal-labels.tooltip':
    'Rota gruplaması için kullanılabilecek rotalara özel etiketler ekleyin.',

  'page.route.configuration.path.rules.required.description':
    'Lütfen geçerli bir HTTP istek yolu girin',
  'page.route.configuration.path.placeholder': 'HTTP istek yolu girin',
  'page.route.configuration.remote_addrs.placeholder': 'İstemci adresi girin',
  'page.route.configuration.host.placeholder': 'HTTP request hostname girin',

  'page.route.service.none': 'Hiçbiri',

  'page.route.rule.create': 'Kural Oluştur',
  'page.route.rule.edit': 'Kural Düzenle',

  'page.route.advanced-match.operator.sample.IN': 'Lütfen bir dizi girin, ör. ["1", "2"]',
  'page.route.advanced-match.operator.sample.~~': 'Reqular Ex. girin ör [a-z]+',
  'page.route.fields.service_id.invalid': 'Lütfen bağlama hizmetinin yapılandırmasını kontrol edin',
  'page.route.fields.service_id.without-upstream':
    'Hizmeti bağlamazsanız, Yukarı Akışı ayarlamanız gerekir (Adım 2)',
  'page.route.advanced-match.tooltip':
    'İstek üstbilgileri, istek parametreleri ve tanımlama bilgileri aracılığıyla rota eşleştirmeyi destekler ve gri tonlamalı yayınlama ve mavi-yeşil test gibi senaryolara uygulanabilir.',
  'page.route.advanced-match.message': 'İpuçları',
  'page.route.advanced-match.tips.requestParameter':
    'İstek Parametresi：İstek URLsinin sorgulanması',
  'page.route.advanced-match.tips.postRequestParameter':
    'POST İstek Parametresi：Yalnızca x-www-form-urlencoding formunu destekler',
  'page.route.advanced-match.tips.builtinParameter':
    'Yerleşik Parametre: Nginx dahili parametreleri destekler',

  'page.route.fields.custom.redirectOption.tooltip': 'Bu yönlendirme eklentisi ile ilgilidir',
  'page.route.fields.service_id.tooltip':
    'Yapılandırmalarını yeniden kullanmak için Hizmet nesnesini bağlayın.',

  'page.route.fields.vars.invalid': 'Lütfen gelişmiş eşleşme koşulu yapılandırmasını kontrol edin',
  'page.route.fields.vars.in.invalid':
    'IN operatörünü kullanırken parametre değerlerini dizi formatında girin.',

  'page.route.data_loader.import': 'İçeri Aktar',
  'page.route.data_loader.import_panel': 'Veriyi içe Aktar',
  'page.route.data_loader.types.openapi3': 'OpenAPI 3',
  'page.route.data_loader.types.openapi_legacy': 'OpenAPI 3 Eski Sürüm',
  'page.route.data_loader.labels.loader_type': 'Veri Yükleyici Tipi',
  'page.route.data_loader.labels.task_name': 'İş Adı',
  'page.route.data_loader.labels.upload': 'Yükle',
  'page.route.data_loader.labels.openapi3_merge_method': 'HTTP Methodlarını birleştir',
  'page.route.data_loader.tips.select_type': 'Lütfen veri yükleyicisini seçin',
  'page.route.data_loader.tips.input_task_name': 'Lütfen içeri aktarılan iş adını belirtin',
  'page.route.data_loader.tips.click_upload': 'Yüklemek için tıkla',
  'page.route.data_loader.tips.openapi3_merge_method':
    'OpenAPI routelarda birden çok HTTP yöntemi tek bir rotada birleştirilir. Routelarda farklı yapılandırmalara sahip birden fazla HTTP yöntemi olduğunda (ör. securitySchema) ve bunları birden çok rotada oluşturmak için bu seçeneği kapatabilirsiniz. ',
};
