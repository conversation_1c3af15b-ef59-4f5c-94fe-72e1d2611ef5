/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
@import '~antd/es/style/themes/default.less';

.container {
  display: flex;
  flex-direction: column;
  overflow: auto;
  background: @layout-body-background;
}

.lang {
  width: 100%;
  height: 42px;
  line-height: 42px;
  text-align: right;
}

a {
  color: black;

  &:hover {
    color: @primary-color;
  }
}

.github {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  font-size: 16px;
  vertical-align: middle;
  cursor: pointer;
}

.content {
  flex: 1;
  padding: 32px 0;
}

@media (min-width: @screen-md-min) {
  .container {
    background-repeat: no-repeat;
    background-position: center 110px;
    background-size: 100%;
  }

  .content {
    padding: 32px 0 24px;
  }
}

.top {
  text-align: center;
}

.header {
  height: 44px;
  line-height: 44px;

  a {
    text-decoration: none;
  }
}

.logo {
  height: 60px;
  vertical-align: top;
}

.title {
  position: relative;
  top: 2px;
  color: @heading-color;
  font-weight: 600;
  font-size: 33px;
  font-family: Avenir, 'Helvetica Neue', Arial, Helvetica, sans-serif;
}

.desc {
  margin-top: 20px;
  margin-bottom: 40px;
  color: @text-color-secondary;
  font-size: @font-size-base;
}

.main {
  width: 368px;
  margin: 0 auto;

  @media screen and (max-width: @screen-sm) {
    width: 95%;
  }

  .icon {
    margin-left: 16px;
    color: rgb(0 0 0 / 20%);
    font-size: 24px;
    vertical-align: middle;
    cursor: pointer;
    transition: color 0.3s;

    &:hover {
      color: @primary-color;
    }
  }

  .submit {
    width: 100%;
  }

  .other {
    margin-top: 24px;
    line-height: 22px;
    text-align: left;

    .register {
      float: right;
    }
  }
}
