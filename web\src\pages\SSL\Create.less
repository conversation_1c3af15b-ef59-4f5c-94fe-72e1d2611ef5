/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
@import '~antd/es/style/themes/default.less';

.card {
  margin-bottom: 24px;
}

.heading {
  margin: 0 0 16px;
  font-size: 14px;
  line-height: 22px;
}

.steps:global(.ant-steps) {
  max-width: 750px;
  margin: 16px auto;
}

.errorIcon {
  margin-right: 24px;
  color: @error-color;
  cursor: pointer;

  span.anticon {
    margin-right: 4px;
  }
}

.errorPopover {
  :global {
    .ant-popover-inner-content {
      min-width: 256px;
      max-height: 290px;
      padding: 0;
      overflow: auto;
    }
  }
}

.errorListItem {
  padding: 8px 16px;
  list-style: none;
  border-bottom: 1px solid @border-color-split;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background: @item-active-bg;
  }

  &:last-child {
    border: 0;
  }

  .errorIcon {
    float: left;
    margin-top: 4px;
    margin-right: 12px;
    padding-bottom: 22px;
    color: @error-color;
  }

  .errorField {
    margin-top: 2px;
    color: @text-color-secondary;
    font-size: 12px;
  }
}

.editable {
  td {
    padding-top: 13px !important;
    padding-bottom: 12.5px !important;
  }
}

// custom footer for fixed footer toolbar
.advancedForm + div {
  padding-bottom: 64px;
}

.advancedForm {
  :global {
    .ant-form .ant-row:last-child .ant-form-item {
      margin-bottom: 24px;
    }

    .ant-table td {
      transition: none !important;
    }
  }
}

.optional {
  color: @text-color-secondary;
  font-style: normal;
}

.button-area {
  display: flex;
  justify-content: center;
}
