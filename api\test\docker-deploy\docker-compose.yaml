#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
version: "3.6"

services:
  etcd:
    image: gcr.io/etcd-development/etcd:v3.4.0
    ports:
      - "2379:2379"
    expose:
      - 2379
      - 2380
    networks:
      apisix_dashboard_e2e:
        ipv4_address: *************
    environment:
      - ETCDCTL_API=3
    command:
      - /usr/local/bin/etcd
      - --data-dir=/etcd-data
      - --name
      - node1
      - --initial-advertise-peer-urls
      - http://*************:2380
      - --listen-peer-urls
      - http://0.0.0.0:2380
      - --advertise-client-urls
      - http://*************:2379
      - --listen-client-urls
      - http://0.0.0.0:2379

  managerapi:
    image: dashboard:ci
    restart: always
    volumes:
      - ../../conf/conf.yaml:/usr/local/apisix-dashboard/conf/conf.yaml:ro
    depends_on:
      - etcd
    ports:
      - '9000:9000/tcp'
    networks:
      apisix_dashboard_e2e:
        ipv4_address: *************

networks:
  apisix_dashboard_e2e:
    driver: bridge
    ipam:
      driver: default
      config:
        -
          subnet: ************/24
