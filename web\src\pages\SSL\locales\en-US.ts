/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.ssl.form.itemLabel.expireTime': 'Expiration Time',
  'page.ssl.form.itemLabel.cert': 'Certificate Content',
  'page.ssl.form.itemRuleMessage.certValueLength':
    'The Certificate Content requires at least 128 characters',
  'page.ssl.form.itemLabel.privateKey': 'Private Key',
  'page.ssl.form.itemRuleMessage.privateKeyLength':
    'The Private Key requires at least 128 characters',

  'page.ssl.button.uploadCert': 'Upload Certificate',

  'page.ssl.form.itemLabel.way': 'Way',
  'page.ssl.select.placeholder.selectCreateWays': 'Please select create ways',
  'page.ssl.selectOption.input': 'Input',
  'page.ssl.upload': 'Upload',

  'page.ssl.notification.updateCertEnableStatusSuccessfully':
    'Update certificate enable status successfully',
  'page.ssl.list': 'SSL List',
  'page.ssl.list.expirationTime': 'Expiration Time',
  'page.ssl.list.ifEnable': 'If Enable',
  'page.ssl.list.periodOfValidity': 'Period Of Validity',
  'page.ssl.steps.stepTitle.completeCertInfo': 'Complete Certificate Information',
  'component.ssl.removeSSLSuccess': 'Remove target SSL successfully',
  'component.ssl.removeSSLItemModalContent': 'You are going to remove this item!',

  'component.ssl.description':
    'The certificate is used by the gateway to process encrypted requests, which will be associated with the SNI and bound to the host name in the Route.',
  'component.ssl.fields.cert.required': 'Please enter the certificate',
  'component.ssl.fields.key.required': 'Please enter the key',
};
