/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.consumer.form.itemRuleMessage.username': '最大长度100，仅支持字母、数字和 _ 。',
  'page.consumer.form.itemExtraMessage.username': '名称需唯一',
  'page.consumer.username': '名称',
  'page.consumer.username.required': '请输入消费者名称',
  'page.consumer.updateTime': '更新时间',
  'page.consumer.list': '消费者列表',
  'page.consumer.description': '消费者是路由的消费方，形式包括开发者、最终用户、API 调用等。',
  'page.consumer.create': '创建消费者',
  'page.consumer.configure': '配置消费者',
};
