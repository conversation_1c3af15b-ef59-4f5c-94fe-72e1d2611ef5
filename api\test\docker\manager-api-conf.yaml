#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# TODO use the original conf/config.yaml + sed, replace of this file.

conf:
  listen:
    host: 0.0.0.0            # `manager api` listening ip or host name. It's for e2e test, so it is set to 0.0.0.0
    port: 9000               # `manager api` listening port
  etcd:
    endpoints:               # supports defining multiple etcd host addresses for an etcd cluster
      - *************:2379   # ips here are defined in docker compose.
      - *************:2379
      - *************:2379
  log:
    error_log:                     # yamllint disable rule:comments-indentation
      level: warn                  # supports levels, lower to higher: debug, info, warn, error, panic, fatal
      file_path: logs/error.log    # supports relative path, absolute path, standard output
                                   # such as: logs/error.log, /tmp/logs/error.log, /dev/stdout, /dev/stderr
authentication:
  secret: secret             # secret for jwt token generation.
                             # *NOTE*: Highly recommended to modify this value to protect `manager api`.
                             # if it's default value, when `manager api` start, it will generate a random string to replace it.
  expire_time: 3600          # jwt token expire time, in second
  users:                     # yamllint enable rule:comments-indentation
    - username: admin        # username and password for login `manager api`
      password: admin
    - username: user
      password: user
