/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'page.proto.list': 'Proto Listesi',
  'page.proto.list.description':
    "Protokol arabellekleri, Google'ın yapılandırılmış verileri seri hale getirmek için dilden bağımsız, platformdan bağımsız, genişletilebilir mekanizmasıdır. Verilerinizin nasıl yapılandırılmasını istediğinizi bir kez tanı<PERSON>ınız, ardından yapılandırılmış verilerinizi kolayca yazıp okumak için özel oluşturulmuş kaynak kodunu kullanabilirsiniz. çeşitli veri akışları ve çeşitli diller kullanır. Protokol arabellekleri listesi, oluşturulan proto dosyalarını içerir. grpc kod dönüştürme eklentisi etkinleştirildiğinde, kimlik ilgili proto dosyalarının içeriğini okumak için yapılandırılabilir.",
  'page.proto.list.edit': 'Proto Düzenle',
  'page.proto.list.confirm.delete': 'Silmek istediğinizden emin misiniz?',
  'page.proto.list.confirm': 'Onay',
  'page.proto.list.cancel': 'İptal',
  'page.proto.list.delete.successfully': 'Proto başarıyla silindi',
  'page.proto.list.delete': 'Sil',
  'page.proto.id.tooltip': '.proto dosya türü',

  'page.proto.desc': 'açıklama',
  'page.proto.desc.tooltip': '.proto dosya açıklaması',

  'page.proto.content': 'içerik',
  'page.proto.content.tooltip': '.proto dosya içeriği',

  'page.proto.drawer.create': 'Proto Oluştur',
  'page.proto.drawer.edit': 'Proto Düzenle',
  'page.proto.drawer.create.successfully': 'Proto başarıyla oluşturuldu',
  'page.proto.drawer.edit.successfully': 'Proto başarıyla düzenlendi',
  'page.proto.drawer.delete.successfully': 'Proto başarıyla silindi',
};
