/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'component.plugin-flow.text.condition.required': '配置判断条件',
  'component.plugin-flow.text.condition': '判断条件',
  'component.plugin-flow.text.condition2': '条件判断',
  'component.plugin-flow.text.condition.placeholder': '请输入判断条件',
  'component.plugin-flow.text.without-data': '存在未配置的元件',
  'component.plugin-flow.text.plugin-without-data.description': '请配置插件：',
  'component.plugin-flow.text.no-start-node': '请关联开始节点',
  'component.plugin-flow.text.no-root-node': '未找到根节点',
  'component.plugin-flow.text.start-node': '开始',
  'component.plugin-flow.text.general': '通用',
  'component.plugin-flow.text.nodes-area': '元件选择区',
  'component.plugin-flow.text.nodes.not-found': '无匹配元件',
  'component.plugin-flow.text.search-nodes.placeholder': '请输入插件元件名称',
  'component.plugin-flow.text.condition-rule.tooltip': '节点的判断条件。例如：code == 503',
  'component.plugin-flow.text.line': '线条',
  'component.plugin-flow.text.grid': '网格',
  'component.plugin-flow.text.background': '背景',
  'component.plugin-flow.text.node': '节点',
  'component.plugin-flow.text.text': '文本',
  'component.plugin-flow.text.condition-without-configuration': '请检查条件判断元件的配置',
  'component.plugin-flow.text.preview.readonly': '请注意：在当前页面，您在画布上地操作不会被保留。',
  'component.plugin-flow.text.both-modes-exist': '编排模式配置将覆盖普通模式配置，是否继续操作？',
  'component.plugin-flow.text.both-modes-exist.title': '配置冲突',
};
