/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'component.plugin-flow.text.condition.required': 'Configure Rule',
  'component.plugin-flow.text.condition': 'Rule',
  'component.plugin-flow.text.condition2': 'Condition',
  'component.plugin-flow.text.condition.placeholder': 'Please enter the rule',
  'component.plugin-flow.text.without-data': 'Found node without configuration',
  'component.plugin-flow.text.plugin-without-data.description': 'Please configure plugin: ',
  'component.plugin-flow.text.no-start-node': 'Please connect the start node',
  'component.plugin-flow.text.no-root-node': 'Root node not found',
  'component.plugin-flow.text.start-node': 'Start',
  'component.plugin-flow.text.general': 'General',
  'component.plugin-flow.text.nodes-area': 'Available Nodes',
  'component.plugin-flow.text.nodes.not-found': 'Not Found',
  'component.plugin-flow.text.search-nodes.placeholder': 'Search plugin by name',
  'component.plugin-flow.text.condition-rule.tooltip':
    'The judgment condition of the node. e.g: code == 503',
  'component.plugin-flow.text.line': 'Line',
  'component.plugin-flow.text.grid': 'Grid',
  'component.plugin-flow.text.background': 'Background',
  'component.plugin-flow.text.node': 'Node',
  'component.plugin-flow.text.text': 'Text',
  'component.plugin-flow.text.condition-without-configuration':
    "Please check all condition nodes' data",
  'component.plugin-flow.text.preview.readonly':
    'NOTE: your actions on the following drawer will not be preserved.',
  'component.plugin-flow.text.both-modes-exist':
    'The orchestration mode configuration will override the normal mode configuration, are you sure to continue?',
  'component.plugin-flow.text.both-modes-exist.title': 'Attention',
};
