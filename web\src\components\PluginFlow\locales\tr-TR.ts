/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'component.plugin-flow.text.condition.required': 'Konfigürasyon kontrolü',
  'component.plugin-flow.text.condition': 'Kural',
  'component.plugin-flow.text.condition2': 'Koşul',
  'component.plugin-flow.text.condition.placeholder': 'Kuralı giriniz',
  'component.plugin-flow.text.without-data': 'Konfigürasyonsuz node bulundu.',
  'component.plugin-flow.text.plugin-without-data.description': '<PERSON>ütfen eklentiyi düzenleyin: ',
  'component.plugin-flow.text.no-start-node': 'Lütfen node başlatmak için çalıştırın',
  'component.plugin-flow.text.no-root-node': 'Root node bulunamadı',
  'component.plugin-flow.text.start-node': 'Başlat',
  'component.plugin-flow.text.general': 'Genel',
  'component.plugin-flow.text.nodes-area': 'Uygun nodelar',
  'component.plugin-flow.text.nodes.not-found': 'Bulunamadı',
  'component.plugin-flow.text.search-nodes.placeholder': 'İsme göre eklenti ara',
  'component.plugin-flow.text.condition-rule.tooltip': 'Nodeların karar durumu. örnek: code == 503',
  'component.plugin-flow.text.line': 'Çizgi',
  'component.plugin-flow.text.grid': 'Kare',
  'component.plugin-flow.text.background': 'Arkaplan',
  'component.plugin-flow.text.node': 'Node',
  'component.plugin-flow.text.text': 'Metin',
  'component.plugin-flow.text.condition-without-configuration':
    'Lütfen tüm durum nodelarının verilerini kontrol edin',
  'component.plugin-flow.text.preview.readonly':
    'NOT: Aşağıdaki çekmecedeki işlemleriniz korunmayacak.',
  'component.plugin-flow.text.both-modes-exist':
    'Orkestrasyon modu yapılandırması normal mod yapılandırmasını geçersiz kılar, emin misiniz?',
  'component.plugin-flow.text.both-modes-exist.title': 'Dikkat',
};
