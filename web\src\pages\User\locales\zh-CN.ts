/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

export default {
  'component.user.login': '登录',
  'component.user.loginMethodPassword': '账号密码登录',
  'component.user.loginMethodPassword.username': '账号',
  'component.user.loginMethodPassword.password': '密码',
  'component.user.loginMethodPassword.inputUsername': '请输入账号！',
  'component.user.loginMethodPassword.inputPassword': '请输入密码！',
  'component.user.loginMethodPassword.incorrectPassword': '账号或密码错误',
  'component.user.loginMethodPassword.fieldInvalid': '请检查账号和密码',
  'component.user.loginMethodPassword.success': '登录成功',
  'component.user.loginMethodPassword.changeDefaultAccount': '如何修改默认账户和密码？',
  'component.user.loginMethodPassword.modificationMethod':
    '请修改 /api/conf/conf.yaml 文件中 users 字段',
};
