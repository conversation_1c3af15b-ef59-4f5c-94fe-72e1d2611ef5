{"username": "#username", "languageSwitcher": ".ant-space-align-center", "dropdown": ".rc-virtual-list", "notification": ".ant-notification-notice-message", "drawerBody": ".ant-drawer-wrapper-body", "notificationCloseIcon": ".ant-notification-close-icon", "notificationDesc": ".ant-notification-notice-description", "errorNotification:": ".ant-notification-notice-error", "pluginCard": ".ant-card", "pluginCardBordered": ".ant-card-bordered", "pageContent": ".ant-pro-page-container", "tableBody": ".ant-table-tbody", "tableCell": ".ant-table-cell", "empty": ".ant-empty-normal", "refresh": ".anticon-reload", "disabledSwitcher": "#disable", "checkedSwitcher": ".ant-switch-checked", "deleteButton": ".ant-btn-dangerous", "name": "#name", "nodes_0_host": "#submitNodes_0_host", "nodes_0_port": "#submitNodes_0_port", "nodes_0_weight": "#submitNodes_0_weight", "upstream_id": "#upstream_id", "input": ":input", "nameSelector": "[title=Name]", "serviceSelector": "[title=test_service]", "nameSearch": "[title=Name]", "description": "#desc", "upstreamSelector": "[data-cy=upstream_selector]", "upstreamNodeMinus0": "[data-cy=upstream-node-minus-0]", "addHost": "[data-cy=addHost]", "hosts_1": "#hosts_1", "remoteHost": "#remote_addrs_0", "remoteAddress": "[data-cy=addRemoteAddr]", "address1": "#remote_addrs_1", "parameterPosition": "#position", "ruleCard": ".ant-modal", "operator": "#operator", "value": "#value", "fileSelector": "[type=file]", "fileTypeRadio": "[type=radio]", "fileSelectorClose": ".ant-modal-close", "debugUri": "#debugUri", "hosts_0": "#hosts_0", "labels_0_labelKey": "#labels_0_labelKey", "labels_0_labelValue": "#labels_0_labelValue", "labelSelector": "[title=Labels]", "labelSelect_0": ".ant-select-selection-overflow", "pageContainer": ".ant-pro-page-container", "notificationMessage": ".ant-notification-notice-message", "avatar": ".ant-space-align-center", "grafanaURL": "#grafanaURL", "explain": ".ant-form-item-explain", "upstreamType": ".ant-select-item-option-content", "errorExplain": ".ant-form-item-explain", "usernameInput": "#control-ref_username", "passwordInput": "#control-ref_password", "drawer": ".ant-drawer-content", "drawerFooter": ".ant-drawer-footer", "monacoScroll": ".monaco-scrollable-element", "drawerClose": ".ant-drawer-close", "descriptionSelector": "[title=Description]", "customSelector": "[title=Custom]", "notificationClose": ".anticon-close", "redirectURIInput": "#redirectURI", "redirectCodeSelector": "#ret_code", "paginationOptions": ".ant-pagination-options", "fiftyPerPage": "[title=\"50 / page\"]", "twentyPerPage": "[title=\"20 / page\"]", "pageList": ".ant-table-pagination-right", "pageTwo": ".ant-pagination-item-2", "pageTwoActived": ".ant-pagination-item-2.ant-pagination-item-active", "selectDropdown": ".ant-select-dropdown", "monacoMode": "[data-cy='monaco-mode']", "selectJSON": ".ant-select-dropdown [label=JSON]", "deleteAlert": ".ant-modal-body"}