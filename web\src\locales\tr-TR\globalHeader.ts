/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export default {
  'component.globalHeader.search': 'Arama',
  'component.globalHeader.search.example1': 'Arama Örneği 1',
  'component.globalHeader.search.example2': 'Arama Örneği 2',
  'component.globalHeader.search.example3': 'Arama Örneği 3',
  'component.globalHeader.help': 'Yardım',
  'component.globalHeader.notification': 'Bildirim',
  'component.globalHeader.notification.empty': 'Bildirim Yok',
  'component.globalHeader.message': 'Mesaj',
  'component.globalHeader.message.empty': 'Mesaj Yok',
  'component.globalHeader.event': 'Olay',
  'component.globalHeader.event.empty': 'Olay Yok',
  'component.noticeIcon.clear': 'Temizle',
  'component.noticeIcon.cleared': 'Temizlendi',
  'component.noticeIcon.empty': 'Bildirim bulunamadı',
  'component.noticeIcon.view-more': 'Daha fazla görüntüle',
};
